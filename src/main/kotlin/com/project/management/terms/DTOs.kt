package com.project.management.terms

data class TermPostRequest(
    val name: String,
    val description: String?,
)

data class TermPatchRequest(
    val name: String? = null,
    val description: String? = null,
    val version: Long
)

data class TermsGroupPostRequest(
    val name: String,
    val description: String?,
)

data class TermsGroupPatchRequest(
    val name: String? = null,
    val description: String? = null,
    val version: Long
)
package com.project.management.terms


import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PatchMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/api/v1/terms")
class TermsController(
    val termsService: TermsService
) {
    @GetMapping
    fun getAll(): ResponseEntity<List<Term>> {
        val terms = termsService.getAll()
        return ResponseEntity.ok(terms)
    }

    @GetMapping("/groups/{groupId}")
    fun getAll(@PathVariable groupId: Long): ResponseEntity<List<Term>> {
        val terms = termsService.getAll(groupId)
        return ResponseEntity.ok(terms)
    }

    @PostMapping("/groups/{groupId}")
    fun create(
        @PathVariable groupId: Long,
        @RequestBody termRequestDto: TermPostRequest,
    ): ResponseEntity<Term> {
        val term = termsService.create(termRequestDto, groupId)
        return ResponseEntity.ok(term)
    }

    @PatchMapping("{termId}")
    fun edit(
        @PathVariable termId: Long,
        @RequestBody request: TermPatchRequest,
    ): ResponseEntity<Term> {
        return ResponseEntity.ok(termsService.edit(request, termId))
    }
}

@RestController
@RequestMapping("/api/v1/terms/groups")
class TermsGroupsController(
    private val termsGroupsService: TermsGroupsService
) {
    @GetMapping
    fun getAll(): ResponseEntity<List<TermsGroup>> {
        return ResponseEntity.ok(termsGroupsService.getAll())
    }

    @PostMapping
    fun create(@RequestBody termsGroupRequestDto: TermsGroupPostRequest): ResponseEntity<TermsGroup> {
        return ResponseEntity.ok(termsGroupsService.create(termsGroupRequestDto))
    }

    @PatchMapping("/{termsGroupId}")
    fun update(
        @RequestBody request: TermsGroupPatchRequest,
        @PathVariable termsGroupId: Long
    ): ResponseEntity<TermsGroup> {
        return ResponseEntity.ok(termsGroupsService.update(request, termsGroupId))
    }
}
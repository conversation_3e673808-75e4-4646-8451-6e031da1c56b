package com.project.management.notes

import com.project.management.users.User

fun NotePostRequest.toModel(performer: User): Note {
    return Note(
        note = note,
        beneficiaryId = beneficiaryId,
        customerId = customerId,
        projectId = projectId,
        organizationId = performer.organizationId,
        createdBy = performer.id!!,
        updatedBy = performer.id!!,
    )
}

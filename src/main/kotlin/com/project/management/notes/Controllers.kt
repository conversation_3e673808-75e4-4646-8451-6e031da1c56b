package com.project.management.notes

import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PatchMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/api/v1/notes")
class NoteController(
    private val noteService: NoteService
) {

    @GetMapping("/beneficiaries/{beneficiaryId}")
    fun getByBeneficiaryId(
        @PathVariable beneficiaryId: Long
    ): ResponseEntity<List<Note>> {
        return ResponseEntity.ok(noteService.getByBeneficiaryId(beneficiaryId = beneficiaryId))
    }

    @GetMapping("/customers/{customerId}")
    fun getByCustomerId(
        @PathVariable customerId: Long
    ): ResponseEntity<List<Note>> {
        return ResponseEntity.ok(noteService.getByCustomerId(customerId = customerId))
    }

    @GetMapping("/projects/{projectId}")
    fun getByProjectId(
        @PathVariable projectId: Long
    ): ResponseEntity<List<Note>> {
        return ResponseEntity.ok(noteService.getByProjectId(projectId = projectId))
    }

    @GetMapping("/{noteId}")
    fun getById(
        @PathVariable noteId: Long
    ): ResponseEntity<Note> {
        return ResponseEntity.ok(noteService.getById(noteId = noteId))
    }

    @PostMapping
    fun create(
        @RequestBody request: NotePostRequest
    ): ResponseEntity<Note> {
        return ResponseEntity.ok(noteService.create(request))
    }

    @PatchMapping("/{noteId}")
    fun edit(
        @PathVariable noteId: Long,
        @RequestBody request: NotePatchRequest
    ): ResponseEntity<Note> {
        return ResponseEntity.ok(noteService.edit(request, noteId))
    }

    @DeleteMapping("/{noteId}")
    fun delete(
        @PathVariable noteId: Long
    ): ResponseEntity<Unit> {
        noteService.delete(noteId)
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build()
    }
}

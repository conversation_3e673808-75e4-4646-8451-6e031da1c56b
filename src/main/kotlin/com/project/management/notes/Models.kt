package com.project.management.notes

import com.project.management.common.entity.ServerEntity
import jakarta.persistence.Entity
import jakarta.persistence.Table
import org.hibernate.annotations.Filter
import org.hibernate.annotations.FilterDef

const val NOTE_DELETED_FILTER = "note_deleted_filter"

@Entity
@Table(name = "notes")
@FilterDef(name = NOTE_DELETED_FILTER, parameters = [])
@Filter(name = NOTE_DELETED_FILTER, condition = "deleted is NULL")
class Note(
    var note: String,
    var beneficiaryId: Long?,
    var customerId: Long?,
    var projectId: Long?,

    override val organizationId: Long,
    override val createdBy: Long,
    override var updatedBy: Long,
) : ServerEntity()

package com.project.management.notes

import com.project.management.common.entity.OrganizationId
import com.project.management.common.entity.validate
import com.project.management.common.exceptions.BusinessException
import com.project.management.common.utility.autowired
import com.project.management.common.utility.filterDeleted
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository

object NoteRepository {
    private val repo: Repo by autowired()

    object Query {
        fun getAll(organizationId: OrganizationId): List<Note> = filterDeleted {
            repo.findAllByOrganizationId(organizationId.value)
        }

        fun getById(organizationId: OrganizationId, id: Long): Note? = filterDeleted {
            repo.findByIdAndOrganizationId(id, organizationId.value)
        }

        fun getAllByBeneficiaryId(organizationId: OrganizationId, beneficiaryId: Long): List<Note> = filterDeleted {
            repo.findAllByBeneficiaryIdAndOrganizationId(beneficiaryId, organizationId.value)
        }

        fun getAllByCustomerId(organizationId: OrganizationId, customerId: Long): List<Note> = filterDeleted {
            repo.findAllByCustomerIdAndOrganizationId(customerId, organizationId.value)
        }

        fun getAllByProjectId(organizationId: OrganizationId, projectId: Long): List<Note> = filterDeleted {
            repo.findAllByProjectIdAndOrganizationId(projectId, organizationId.value)
        }
    }

    object Validate {
        fun noteExists(organizationId: OrganizationId, noteId: Long): Note {
            return Query.getById(organizationId, noteId).validate("Note not found")
        }

        fun validCreateNoteRequest(request: NotePostRequest): NotePostRequest {
            if (request.beneficiaryId == null && request.customerId == null && request.projectId == null) {
                throw BusinessException
                    .BadRequestException("At least one of beneficiaryId, customerId or projectId must be provided")
            }
            return request
        }

        fun validPatchNoteRequest(request: NotePatchRequest): NotePatchRequest {
            if (request.beneficiaryId == null && request.customerId == null && request.projectId == null) {
                throw BusinessException
                    .BadRequestException("At least one of beneficiaryId, customerId or projectId must be provided")
            }
            return request
        }
    }

    object Mutate {
        fun save(note: Note): Note {
            return repo.save(note)
        }

        fun deleteById(organizationId: OrganizationId, id: Long, updatedBy: Long) {
            repo.deleteByIdAndOrganizationId(id, organizationId.value, updatedBy)
        }
    }

    private fun <T> filterDeleted(block: () -> T) = filterDeleted(NOTE_DELETED_FILTER, block)
}

@Repository
private interface Repo : JpaRepository<Note, Long> {

    fun findAllByOrganizationId(organizationId: Long): List<Note>

    fun findAllByBeneficiaryIdAndOrganizationId(
        beneficiaryId: Long,
        organizationId: Long
    ): List<Note>

    fun findAllByCustomerIdAndOrganizationId(
        customerId: Long,
        organizationId: Long
    ): List<Note>

    fun findAllByProjectIdAndOrganizationId(projectId: Long, organizationId: Long): List<Note>

    fun findByIdAndOrganizationId(id: Long, organizationId: Long): Note?

    @Modifying
    @Query(
        value = "UPDATE notes SET deleted = NOW(), updated_by = :updatedBy WHERE id = :id AND organization_id = :organizationId AND deleted IS NULL",
        nativeQuery = true,
    )
    fun deleteByIdAndOrganizationId(id: Long, organizationId: Long, updatedBy: Long)
}

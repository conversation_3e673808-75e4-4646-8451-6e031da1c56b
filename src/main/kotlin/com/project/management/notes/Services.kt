package com.project.management.notes

import com.project.management.common.configuration.CurrentUserConfig
import com.project.management.common.entity.OrganizationId
import com.project.management.common.exceptions.BusinessException
import org.springframework.data.jpa.domain.AbstractPersistable_.id
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class NoteService(
    private val currentUser: CurrentUserConfig,
) {

    fun getByBeneficiaryId(beneficiaryId: Long): List<Note> {
        val user = currentUser.getCurrentUser()
        return NoteRepository.Query.getAllByBeneficiaryId(
            organizationId = user, beneficiaryId = beneficiaryId
        )
    }

    fun getByCustomerId(customerId: Long): List<Note> {
        val user = currentUser.getCurrentUser()
        return NoteRepository.Query.getAllByCustomerId(
            organizationId = user, customerId = customerId
        )
    }

    fun getByProjectId(projectId: Long): List<Note> {
        val user = currentUser.getCurrentUser()
        return NoteRepository.Query.getAllByProjectId(
            organizationId = user, projectId = projectId
        )
    }

    fun getById(noteId: Long): Note {
        val loggedInUser = currentUser.getCurrentUser()
        return NoteRepository.Validate.noteExists(
            organizationId = loggedInUser, noteId = noteId
        )
    }

    @Transactional
    fun create(request: NotePostRequest): Note {
        val loggedInUser = currentUser.getCurrentUser()
        val validatedRequest = NoteRepository.Validate.validCreateNoteRequest(request)
        val note = validatedRequest.toModel(loggedInUser)
        return NoteRepository.Mutate.save(note)
    }

    @Transactional
    fun edit(request: NotePatchRequest, noteId: Long): Note {
        val loggedInUser = currentUser.getCurrentUser()
        val note = NoteRepository.Validate.noteExists(
            organizationId = loggedInUser, noteId = noteId
        )
        val validatedRequest = NoteRepository.Validate.validPatchNoteRequest(request)
        if (note.version != validatedRequest.version) throw BusinessException.ConflictException()
        note.note = validatedRequest.note ?: note.note
        note.beneficiaryId = validatedRequest.beneficiaryId ?: note.beneficiaryId
        note.customerId = validatedRequest.customerId ?: note.customerId
        note.projectId = validatedRequest.projectId ?: note.projectId
        note.updatedBy = loggedInUser.id

        return NoteRepository.Mutate.save(note)
    }

    @Transactional
    fun delete(noteId: Long) {
        val loggedInUser = currentUser.getCurrentUser()
        val note = NoteRepository.Validate.noteExists(
            organizationId = loggedInUser, noteId = noteId
        )
        NoteRepository.Mutate.deleteById(
            organizationId = loggedInUser, id = noteId, updatedBy = loggedInUser.id
        )
    }
}

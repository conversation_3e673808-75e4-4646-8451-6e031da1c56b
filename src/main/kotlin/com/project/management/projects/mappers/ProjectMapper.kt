package com.project.management.projects.mappers

import com.project.management.common.annotation.MappingOrganizationEntity
import com.project.management.beneficiaries.requests.PostRequestBeneficiaryTransaction
import com.project.management.customers.CustomerTransactionPostRequest
import com.project.management.projects.requests.PostRequestProjectExpense
import com.project.management.projects.requests.PostRequestProjectIncome
import com.project.management.projects.requests.PostRequestProject
import com.project.management.customers.Customer
import com.project.management.customers.CustomerTransaction
import com.project.management.projects.models.ProjectEntity
import com.project.management.projects.models.ProjectExpense
import com.project.management.projects.models.ProjectExpenseEntity
import com.project.management.projects.models.ProjectIncomeEntity
import org.mapstruct.Mapper
import org.mapstruct.Mapping
import org.mapstruct.ReportingPolicy
import java.math.BigDecimal
import java.time.ZonedDateTime

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.ERROR)
interface ProjectMapper {

    @MappingOrganizationEntity
    @Mapping(target = "name", source = "projectRequestDto.name")
    @Mapping(target = "customer", source = "customer")
    fun toProject(
        projectRequestDto: PostRequestProject,
        createdAt: ZonedDateTime = ZonedDateTime.now(),
        updatedAt: ZonedDateTime = createdAt,
        totalPaidExpenses: BigDecimal,
        totalPaidIncomes: BigDecimal,
        customer: Customer,
        organizationId: Long,
        totalExpenses: BigDecimal,
        totalIncomes: BigDecimal,
        version: Long,
        isActive: Boolean,
        userId: Long?
    ): ProjectEntity
}

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.ERROR)
interface ProjectExpenseMapper {
    fun toBeneficiaryTransactionRequestDto(
        projectExpenseRequestDto: PostRequestProjectExpense,
        projectId: Long,
    ): PostRequestBeneficiaryTransaction
}

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.ERROR)
interface ProjectIncomeMapper {

    @MappingOrganizationEntity
    @Mapping(target = "customer", source = "customer")
    @Mapping(target = "customerTransaction", source = "customerTransaction")
    @Mapping(target = "customerId", source = "projectIncomeRequestDto.customerId")
    @Mapping(target = "version", source = "version")
    fun toProjectIncome(
        projectIncomeRequestDto: PostRequestProjectIncome,
        createdAt: ZonedDateTime = ZonedDateTime.now(),
        updatedAt: ZonedDateTime = createdAt,
        customer: Customer,
        project: ProjectEntity,
        customerTransaction: CustomerTransaction,
        customerTransactionId: Long,
        organizationId: Long,
        projectId: Long,
        userId: Long?,
        version: Long
    ): ProjectIncomeEntity

    fun toCustomerTransactionRequestDto(
        projectIncomeRequestDto: PostRequestProjectIncome,
        projectId: Long,
    ): CustomerTransactionPostRequest
}

internal fun ProjectExpense.toEntity(): ProjectExpenseEntity {
    return ProjectExpenseEntity(
        organizationId = organizationId,
        beneficiaryId = beneficiaryId,
        beneficiaryTransactionId = beneficiaryTransactionId,
        termsGroupId = termsGroupId,
        termId = termId,
        projectId = projectId,
        id = id,
        createdBy = createdBy,
        updatedBy = updatedBy,
        createdAt = createdAt,
        updatedAt = updatedAt,
        version = version
    )
}
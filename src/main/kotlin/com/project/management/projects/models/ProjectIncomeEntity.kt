package com.project.management.projects.models

import com.project.management.customers.Customer
import com.project.management.customers.CustomerTransaction
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.FetchType
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import jakarta.persistence.JoinColumn
import jakarta.persistence.OneToOne
import jakarta.persistence.PreUpdate
import jakarta.persistence.Table
import org.hibernate.annotations.SQLRestriction
import java.time.ZoneOffset
import java.time.ZonedDateTime

@SQLRestriction("deleted IS NULL")
@Entity
@Table(name = "project_incomes")
data class ProjectIncomeEntity(
    val organizationId: Long,

    @Column(name = "customer_id")
    val customerId: Long,
    @Column(name = "customer_transaction_id")
    val customerTransactionId: Long,
    @Column(name = "project_id")
    val projectId: Long,

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "customer_id", updatable = false, insertable = false)
    val customer: Customer,
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "customer_transaction_id", updatable = false, insertable = false)
    val customerTransaction: CustomerTransaction,
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "project_id", updatable = false, insertable = false)
    val project: ProjectEntity,

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    var createdBy: Long? = null,
    var updatedBy: Long? = null,

    @Column(updatable = false)
    var createdAt: ZonedDateTime = ZonedDateTime.now(ZoneOffset.UTC),
    var updatedAt: ZonedDateTime = createdAt,
    var version: Long
) {
    @PreUpdate
    fun setLastUpdate() {
        version += 1
        updatedAt = ZonedDateTime.now(ZoneOffset.UTC)
    }
}
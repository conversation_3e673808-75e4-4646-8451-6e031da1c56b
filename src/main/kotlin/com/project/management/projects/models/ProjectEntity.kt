package com.project.management.projects.models

import com.project.management.customers.Customer
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.FetchType
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import jakarta.persistence.JoinColumn
import jakarta.persistence.OneToOne
import jakarta.persistence.PreUpdate
import jakarta.persistence.Table
import org.hibernate.annotations.SQLRestriction
import java.math.BigDecimal
import java.time.ZoneOffset
import java.time.ZonedDateTime

@SQLRestriction("deleted IS NULL")
@Entity
@Table(name = "projects")
data class ProjectEntity(
    val name: String,
    val organizationId: Long,
    val description: String,
    val totalExpenses: BigDecimal,
    val totalPaidExpenses: BigDecimal,
    val totalIncomes: BigDecimal,
    val totalPaidIncomes: BigDecimal,
    val isActive: Boolean,
    @Column(name = "customer_id")
    val customerId: Long,
    var version: Long,

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "customer_id", updatable = false, insertable = false)
    val customer: Customer,

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    var createdBy: Long? = null,
    var updatedBy: Long? = null,

    @Column(updatable = false)
    var createdAt: ZonedDateTime = ZonedDateTime.now(ZoneOffset.UTC),
    var updatedAt: ZonedDateTime = createdAt,
) {
    @PreUpdate
    fun setLastUpdate() {
        version += 1
        updatedAt = ZonedDateTime.now(ZoneOffset.UTC)
    }
}
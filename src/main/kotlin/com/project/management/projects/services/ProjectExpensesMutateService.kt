package com.project.management.projects.services

import com.project.management.beneficiaries.repositories.BeneficiaryTransactionMutateRepository
import com.project.management.common.configuration.CurrentUserConfig
import com.project.management.common.entity.validate
import com.project.management.common.exceptions.BusinessException
import com.project.management.projects.models.ProjectExpense
import com.project.management.projects.requests.PatchRequestProjectExpense
import com.project.management.money.MoneyService
import com.project.management.projects.mappers.toEntity
import com.project.management.projects.repositories.ProjectExpenseMutateRepository
import com.project.management.projects.validators.ProjectExpenseValidator
import com.project.management.terms.TermRepository
import com.project.management.terms.TermsGroupRepository
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.ZonedDateTime

@Service
class ProjectExpensesMutateService(
    private val projectExpenseRepository: ProjectExpenseMutateRepository,
    private val currentUser: CurrentUserConfig,

    private val projectExpenseValidator: ProjectExpenseValidator,
    private val beneficiaryTransaction: BeneficiaryTransactionMutateRepository,
    private val money: MoneyService
) {

    @Transactional
    fun updateProjectExpense(
        request: PatchRequestProjectExpense,
        projectExpenseId: Long
    ): ProjectExpense {
        val loggedInUser = currentUser.getCurrentUser()
        val expense = projectExpenseValidator.validateExistsByIdAndOrganization(
            projectExpenseId = projectExpenseId,
            organizationId = loggedInUser.organizationId
        ).toEntity()

        // Check version conflict early
        if (expense.version != request.version) {
            throw BusinessException.ConflictException()
        }

        // Validate terms consistency if provided
        validateTermsConsistency(request.termId, request.termsGroupId, loggedInUser.organizationId)

        val updatedExpense = expense.copy(
            termsGroupId = request.termsGroupId ?: expense.termsGroupId,
            termId = request.termId ?: expense.termId,
            updatedBy = loggedInUser.id
        )
        val projectExpenseEntity = projectExpenseRepository.save(updatedExpense)

        return projectExpenseValidator.validateExistsByIdAndOrganization(
            projectExpenseId = projectExpenseEntity.id!!,
            organizationId = loggedInUser.organizationId
        )
    }

    @Transactional
    fun update(request: PatchRequestProjectExpense, projectExpenseId: Long): ProjectExpense {
        val loggedInUser = currentUser.getCurrentUser()
        val expense = projectExpenseValidator.validateExistsByIdAndOrganization(
            projectExpenseId = projectExpenseId,
            organizationId = loggedInUser.organizationId
        )
        if (expense.version != request.version) {
            throw BusinessException.ConflictException()
        }
        if (expense.beneficiaryTransaction.version != request.transactionVersion) {
            throw BusinessException.ConflictException()
        }

        validateTermsConsistency(request.termId, request.termsGroupId, loggedInUser.organizationId)

        if (
            expense.beneficiaryTransaction.amount.compareTo(request.amount.toBigDecimal()) != 0 ||
            expense.beneficiaryTransaction.amountPaid.compareTo(request.amountPaid.toBigDecimal()) != 0
        ) {
            money.projectExpenseUpdate(request.toAmountRequest(), projectExpenseId)
        }

        if (expense.projectId != request.projectId) {
            money.projectExpenseChangeProject(expense.id!!, expense.projectId, request.projectId)
        }

        if (expense.beneficiaryId != request.beneficiaryId) {
            money.projectExpenseChangeBeneficiary(
                expenseId = expense.id!!,
                oldBeneficiaryId = expense.beneficiaryId,
                newBeneficiaryId = request.beneficiaryId
            )
        }

        if (expense.termsGroupId != request.termsGroupId || expense.termId != request.termId) {
            expense.termId = request.termId
            expense.termsGroupId = request.termsGroupId
            expense.updatedBy = loggedInUser.id
        }

        if (expense.beneficiaryTransaction.description != request.description) {
            expense.beneficiaryTransaction.description = request.description
            expense.beneficiaryTransaction.updatedBy = loggedInUser.id
        }

        if (expense.beneficiaryTransaction.transactionDate.toString() != request.transactionDate) {
            expense.beneficiaryTransaction.transactionDate = ZonedDateTime.parse(request.transactionDate)
            expense.beneficiaryTransaction.updatedBy = loggedInUser.id
        }
        beneficiaryTransaction.save(expense.beneficiaryTransaction)

        projectExpenseRepository.save(expense.toEntity())

        return projectExpenseValidator.validateExistsByIdAndOrganization(
            projectExpenseId = expense.id!!,
            organizationId = loggedInUser.organizationId
        )
    }

    private fun validateTermsConsistency(termId: Long?, termsGroupId: Long?, organizationId: Long) {
        // Early return if no terms provided
        if (termId == null && termsGroupId == null) return

        // Validate that both terms are provided together
        if (termId != null && termsGroupId == null) {
            throw BusinessException.BadRequestException(message = "Both termsGroupId and termId must be provided together.")
        }

        if (termsGroupId != null && termId == null) {
            throw BusinessException.BadRequestException(message = "Both termsGroupId and termId must be provided together.")
        }
        val user = currentUser.getCurrentUser()

        // Validate terms when both are provided
        val term = TermRepository.Query
            .getById(organizationId = currentUser.getCurrentUser(), id = termId!!).validate()
        val termsGroup = TermsGroupRepository.Query
            .getById(id = termsGroupId!!, organizationId = user).validate()

        if (term.termsGroupId != termsGroup.id) {
            throw BusinessException.BadRequestException(message = "Term and TermsGroup do not match.")
        }
    }
}
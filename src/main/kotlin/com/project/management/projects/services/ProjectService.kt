package com.project.management.projects.services

import com.project.management.common.configuration.CurrentUserConfig
import com.project.management.common.entity.OrganizationId
import com.project.management.common.entity.validate
import com.project.management.common.exceptions.BusinessException
import com.project.management.projects.requests.PostRequestProjectAccess
import com.project.management.projects.requests.PostRequestProject
import com.project.management.projects.models.ProjectEntity
import com.project.management.projects.models.ProjectAccessEntity
import com.project.management.projects.mappers.ProjectMapper
import com.project.management.projects.requests.PatchRequestProject
import com.project.management.projects.repositories.ProjectAccessRepository
import com.project.management.projects.repositories.ProjectRepository
import com.project.management.customers.CustomerRepository
import com.project.management.common.entity.validate
import com.project.management.projects.validators.ProjectValidator
import com.project.management.users.UserRepository
import jakarta.transaction.Transactional
import org.springframework.stereotype.Service
import java.math.BigDecimal

@Service
class ProjectService(
    private val projectRepository: ProjectRepository,
    private val projectAccessRepository: ProjectAccessRepository,
    private val currentUser: CurrentUserConfig,
    private val projectMapper: ProjectMapper,
    private val projectValidator: ProjectValidator,
    private val customerValidator: CustomerValidator,
) {

    fun getAll(): List<ProjectEntity> {
        val user = currentUser.getCurrentUser()
        val projects = if (user.isAdmin) {
            projectRepository.findAllByOrganizationId(user.organizationId)
        } else {
            val access = projectAccessRepository.findAllByOrganizationIdAndUserId(
                organizationId = user.organizationId,
                userId = user.id!!
            )
            projectRepository.findAllById(access.map { it.projectId })
        }
        return projects
    }

    fun getById(projectId: Long): ProjectEntity {
        val user = currentUser.getCurrentUser()
        val project = projectValidator.validateExistsByIdAndOrganizationId(
            projectId = projectId,
            organizationId = user.organizationId
        )
        return project
    }

    @Transactional
    fun create(projectRequestDto: PostRequestProject): ProjectEntity {
        val user = currentUser.getCurrentUser()
        val customer = customerValidator.validateCustomerExistsByIdAndOrganizationId(
            customerId = projectRequestDto.customerId,
            organizationId = user.organizationId
        )
        val project = projectMapper.toProject(
            projectRequestDto = projectRequestDto,
            totalExpenses = BigDecimal.ZERO,
            totalPaidExpenses = BigDecimal.ZERO,
            totalIncomes = BigDecimal.ZERO,
            totalPaidIncomes = BigDecimal.ZERO,
            organizationId = user.organizationId,
            isActive = true,
            userId = user.id,
            customer = customer,
            version = 1,
        )

        return projectRepository.save(project)
    }

    @Transactional
    fun addAccess(request: PostRequestProjectAccess, projectId: Long) {
        val user = currentUser.getCurrentUser()
        val project = projectValidator.validateExistsByIdAndOrganizationId(
            projectId,
            user.organizationId
        )
        val projectAccess = ProjectAccessEntity(
            userId = request.userId,
            projectId = projectId,
            organizationId = user.organizationId,
            expensesAccess = request.expensesAccess,
            incomesAccess = request.incomesAccess
        )
        projectAccess.createdBy = user.id
        projectAccess.updatedBy = user.id

        projectAccessRepository.save(projectAccess)
    }

    @Transactional
    fun modifyAccess(request: PostRequestProjectAccess, projectId: Long) {
        val loggedInUser = currentUser.getCurrentUser()
        val project = projectValidator.validateExistsByIdAndOrganizationId(
            projectId = projectId,
            organizationId = loggedInUser.organizationId
        )
        val user = UserRepository.Query.getById(loggedInUser, request.userId).validate()
        var projectAccess = projectAccessRepository
            .findByUserIdAndProjectId(request.userId, projectId)
            ?: throw BusinessException.NotFoundException(message = "Project access does not exist.")

        if (projectAccess.version != request.version) {
            throw BusinessException.ConflictException(message = "Conflicting request, please try again.")
        }

        projectAccess = projectAccess.copy(
            expensesAccess = request.expensesAccess,
            incomesAccess = request.incomesAccess,
            updatedBy = loggedInUser.id!!
        )

        projectAccessRepository.save(projectAccess)
    }

    @Transactional
    fun removeAccess(projectId: Long, userId: Long) {
        val loggedInUser = currentUser.getCurrentUser()
        val project = projectValidator.validateExistsByIdAndOrganizationId(
            projectId,
            loggedInUser.organizationId
        )

        projectAccessRepository.deleteByProjectIdAndUserId(
            projectId = projectId,
            userId = userId,
            updatedBy = loggedInUser.id!!
        )
    }

    @Transactional
    fun editProject(request: PatchRequestProject, projectId: Long): ProjectEntity {
        val loggedInUser = currentUser.getCurrentUser()
        val project = projectValidator.validateExistsByIdAndOrganizationId(
            projectId = projectId,
            organizationId = loggedInUser.organizationId
        )
        if (request.version != project.version) {
            throw BusinessException.ConflictException(message = "Conflicting request, please try again.")
        }
        val customer = if (request.customerId != null) {
            customerValidator.validateCustomerExistsByIdAndOrganizationId(
                customerId = request.customerId,
                organizationId = loggedInUser.organizationId
            )
        } else {
            null
        }
        val updatedProject = project.copy(
            name = request.name ?: project.name,
            description = request.description ?: project.description,
            customerId = request.customerId ?: project.customerId,
            customer = customer ?: project.customer,
            updatedBy = loggedInUser.id!!
        )

        return projectRepository.save(updatedProject)
    }

    @Transactional
    fun delete(projectId: Long) {
        val loggedInUser = currentUser.getCurrentUser()
        val project = projectValidator.validateExistsByIdAndOrganizationId(
            projectId = projectId,
            organizationId = loggedInUser.organizationId
        )

        if (project.totalExpenses.toDouble() != 0.0|| project.totalIncomes.toDouble() != 0.0) {
            throw BusinessException.BadRequestException(message = "Cannot delete project with incomes (${project.totalIncomes}) or expenses (${project.totalExpenses}).")
        }

        projectRepository.deleteByIdAndOrganizationId(
            id = projectId,
            organizationId = loggedInUser.organizationId,
            updatedBy = loggedInUser.id!!
        )
    }
}


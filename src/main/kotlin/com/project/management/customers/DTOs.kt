package com.project.management.customers

import com.project.management.projects.requests.PatchRequestProjectIncomeAmount

data class CustomerPostRequest(
    val name: String,
    val phoneNumber: String,
    val secondaryPhoneNumber: String?,
)

data class CustomerPatchPatch(
    val name: String?,
    val phoneNumber: String?,
    val version: Long
)

data class CustomerTransactionPostRequest(
    val amount: Double,
    val amountPaid: Double,
    val description: String,
    val projectId: Long,
    val transactionDate: String
)

data class CustomerTransactionPatchPatch(
    val description: String?,
    val version: Long
)

data class PostRequestProjectIncomePay(
    val amountPaid: Double,
    val version: Long,
) {
    fun toModifyAmount(amount: Double): PatchRequestProjectIncomeAmount {
        return PatchRequestProjectIncomeAmount(
            amount = amount,
            amountPaid = amountPaid,
            transactionVersion = version
        )
    }
}
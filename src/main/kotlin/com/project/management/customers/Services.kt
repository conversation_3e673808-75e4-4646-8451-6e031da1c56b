package com.project.management.customers

import com.project.management.common.configuration.CurrentUserConfig
import com.project.management.common.exceptions.BusinessException
import com.project.management.common.entity.OrganizationId
import com.project.management.common.entity.validate
import com.project.management.projects.services.ProjectIncomesService
import jakarta.transaction.Transactional
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.annotation.Lazy
import org.springframework.stereotype.Service

@Service
class CustomerService(
    private val currentUser: CurrentUserConfig,
) {

    fun getAll(): List<Customer> {
        val user = currentUser.getCurrentUser()
        val customers = CustomerRepository.Query.getAll(OrganizationId(user.organizationId))
        return customers
    }

    fun getById(customerId: Long): Customer {
        val user = currentUser.getCurrentUser()
        val customer = CustomerRepository.Query
            .getById(OrganizationId(user.organizationId), customerId).validate()
        return customer
    }

    @Transactional
    fun create(request: CustomerPostRequest): Customer {
        val user = currentUser.getCurrentUser()
        val customer = request.toModel(user)

        return CustomerRepository.Mutate.save(customer)
    }

    @Transactional
    fun edit(
        request: CustomerPatchPatch,
        customerId: Long
    ): Customer {
        val loggedInUser = currentUser.getCurrentUser()
        val customer = CustomerRepository.Query
            .getById(OrganizationId(loggedInUser.organizationId), customerId).validate()

        if (customer.version != request.version) throw BusinessException.ConflictException()

        if (request.name != null) customer.name = request.name
        if (request.phoneNumber != null) {
            customer.phoneNumber = request.phoneNumber
            customer.secondaryPhoneNumber = request.phoneNumber
        }
        customer.updatedBy = loggedInUser.id!!

        return CustomerRepository.Mutate.save(customer)
    }
}

@Service
class CustomerTransactionService(
    private val currentUser: CurrentUserConfig,
) {
    fun getAll(): List<CustomerTransaction> {
        val user = currentUser.getCurrentUser()
        val customerTransactions = CustomerTransactionRepository.Query.getAll(OrganizationId(user.organizationId))
        return customerTransactions
    }

    fun getAllByCustomerId(customerId: Long): List<CustomerTransaction> {
        val user = currentUser.getCurrentUser()
        val customer = CustomerRepository.Query
            .getById(OrganizationId(user.organizationId), customerId).validate()
        val customerTransactions = CustomerTransactionRepository.Query
            .getAllByCustomerId(OrganizationId(user.organizationId), customer.id!!)
        return customerTransactions
    }

    @Transactional
    fun create(transaction: CustomerTransactionPostRequest, customerId: Long): CustomerTransaction {
        val user = currentUser.getCurrentUser()
        val customer = CustomerRepository.Query
            .getById(OrganizationId(user.organizationId), customerId).validate()
        val customerTransaction = transaction.toEntity(
            customerId = customer.id!!, user = user
        )
        val result = CustomerTransactionRepository.Mutate.save(customerTransaction)
        return result
    }

    @Transactional
    fun updateTransaction(
        request: CustomerTransactionPatchPatch,
        customerTransactionId: Long
    ): CustomerTransaction {
        val loggedInUser = currentUser.getCurrentUser()
        val transaction = CustomerTransactionRepository.Query
            .getById(OrganizationId(loggedInUser.organizationId), customerTransactionId).validate()

        if (transaction.version != request.version) throw BusinessException.ConflictException()

        val updatedTransaction = transaction
        updatedTransaction.description = request.description ?: transaction.description
        updatedTransaction.updatedBy = loggedInUser.id!!

        return CustomerTransactionRepository.Mutate.save(updatedTransaction)
    }

    @Transactional
    fun delete(customerTransactionId: Long) {
        val loggedInUser = currentUser.getCurrentUser()
        val transaction = CustomerTransactionRepository.Query
            .getById(OrganizationId(loggedInUser.organizationId), customerTransactionId).validate()
        CustomerTransactionRepository.Mutate.deleteById(
            id = customerTransactionId,
            organizationId = OrganizationId(loggedInUser.organizationId),
            updatedBy = loggedInUser.id!!
        )
    }
}
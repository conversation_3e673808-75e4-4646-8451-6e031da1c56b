package com.project.management.customers

import com.project.management.money.MoneyService
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PatchMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/api/v1/customers")
class CustomerController(
    private val customerService: CustomerService
) {
    @GetMapping
    fun getAll(): ResponseEntity<List<Customer>> {
        return ResponseEntity.ok(customerService.getAll())
    }

    @GetMapping("/{customerId}")
    fun getById(@PathVariable customerId: Long): ResponseEntity<Customer> {
        return ResponseEntity.ok(customerService.getById(customerId))
    }

    @PostMapping
    fun create(@RequestBody request: CustomerPostRequest): ResponseEntity<Customer> {
        return ResponseEntity.ok(customerService.create(request))
    }

    @PatchMapping("/{customerId}")
    fun edit(
        @PathVariable customerId: Long,
        @RequestBody request: CustomerPatchPatch,
    ): ResponseEntity<Customer> {
        return ResponseEntity.ok(customerService.edit(request, customerId))
    }
}

@RestController
@RequestMapping("/api/v1/customers/{customerId}/transactions")
class CustomerTransactionController(
    private val customerTransactionService: CustomerTransactionService,
    private val money: MoneyService
) {
    @GetMapping
    fun getAll(
        @PathVariable customerId: Long
    ): ResponseEntity<List<CustomerTransaction>> {
        return ResponseEntity.ok(customerTransactionService.getAllByCustomerId(customerId))
    }

    @PostMapping
    fun create(
        @PathVariable customerId: Long,
        @RequestBody request: CustomerTransactionPostRequest,
    ): ResponseEntity<CustomerTransaction> {
        return ResponseEntity.ok(
            customerTransactionService.create(request, customerId)
        )
    }

    @PostMapping("/{customerTransactionId}/pay")
    fun changeAmountPaid(
        @PathVariable customerTransactionId: Long,
        @RequestBody request: PostRequestProjectIncomePay
    ): ResponseEntity<CustomerTransaction> {
        return ResponseEntity.ok(
            money.projectIncomePay(request, customerTransactionId).customerTransaction
        )
    }

    @PatchMapping("/{customerTransactionId}")
    fun modifyTransaction(
        @PathVariable customerTransactionId: Long,
        @RequestBody request: CustomerTransactionPatchPatch
    ): ResponseEntity<CustomerTransaction> {
        return ResponseEntity.ok(
            customerTransactionService.updateTransaction(request, customerTransactionId)
        )
    }
}
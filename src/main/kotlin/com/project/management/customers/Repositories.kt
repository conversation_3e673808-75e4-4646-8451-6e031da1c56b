package com.project.management.customers

import com.project.management.common.entity.OrganizationId
import com.project.management.common.utility.autowired
import com.project.management.common.utility.filterDeleted
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.math.BigDecimal

object CustomerRepository {
    private val repo: Repo by autowired()

    object Query {
        fun getAll(organizationId: OrganizationId): List<Customer> = filterDeleted {
            repo.findAllByOrganizationId(organizationId.value)
        }

        fun getById(organizationId: OrganizationId, id: Long): Customer? = filterDeleted {
            repo.findByIdAndOrganizationId(id, organizationId.value)
        }

        fun sumAllBalance(organizationId: OrganizationId): BigDecimal = filterDeleted {
            repo.sumBalanceAccumulatorByOrganizationId(organizationId.value)
        }

        fun sumAllPaid(organizationId: OrganizationId): BigDecimal = filterDeleted {
            repo.sumPaidAccumulatorByOrganizationId(organizationId.value)
        }
    }

    object Mutate {
        fun save(customer: Customer): Customer {
            return repo.save(customer)
        }
    }

    private fun <T> filterDeleted(block: () -> T) = filterDeleted(CUSTOMER_DELETED_FILTER, block)
}

object CustomerTransactionRepository {
    private val repo: TransactionRepo by autowired()

    object Query {
        fun getAll(organizationId: OrganizationId): List<CustomerTransaction> = filterDeleted {
            repo.findAllByOrganizationId(organizationId.value)
        }

        fun getById(organizationId: OrganizationId, id: Long): CustomerTransaction? = filterDeleted {
            repo.findByIdAndOrganizationId(id, organizationId.value)
        }

        fun getAllByCustomerId(organizationId: OrganizationId, customerId: Long): List<CustomerTransaction> = filterDeleted {
            repo.findAllByOrganizationIdAndCustomerId(organizationId.value, customerId)
        }

        fun sumAllAmount(organizationId: OrganizationId): BigDecimal = filterDeleted {
            repo.sumAmountByOrganizationId(organizationId.value)
        }

        fun sumAllAmountPaid(organizationId: OrganizationId): BigDecimal = filterDeleted {
            repo.sumAmountPaidByOrganizationId(organizationId.value)
        }

        fun sumAmountByCustomer(organizationId: OrganizationId, customerId: Long): BigDecimal = filterDeleted {
            repo.sumAmountByOrganizationIdAndCustomerId(organizationId.value, customerId)
        }

        fun sumAmountPaidByCustomer(organizationId: OrganizationId, customerId: Long): BigDecimal = filterDeleted {
            repo.sumAmountPaidByOrganizationIdAndCustomerId(organizationId.value, customerId)
        }
    }

    object Mutate {
        fun save(transaction: CustomerTransaction): CustomerTransaction {
            return repo.save(transaction)
        }

        fun deleteById(id: Long, organizationId: OrganizationId, updatedBy: Long) {
            repo.deleteByIdAndOrganizationId(id, organizationId.value, updatedBy)
        }
    }

    private fun <T> filterDeleted(block: () -> T) = filterDeleted(CUSTOMER_TRANSACTION_DELETED_FILTER, block)
}

@Repository
private interface Repo : JpaRepository<Customer, Long> {

    fun findAllByOrganizationId(organizationId: Long): List<Customer>

    fun findByIdAndOrganizationId(id: Long, organizationId: Long): Customer?

    // SUM queries for customer accumulators
    @Query(
        value = "SELECT COALESCE(SUM(balance_accumulator), 0) FROM customers WHERE organization_id = :organizationId AND deleted IS NULL",
        nativeQuery = true
    )
    fun sumBalanceAccumulatorByOrganizationId(organizationId: Long): BigDecimal

    @Query(
        value = "SELECT COALESCE(SUM(paid_accumulator), 0) FROM customers WHERE organization_id = :organizationId AND deleted IS NULL",
        nativeQuery = true
    )
    fun sumPaidAccumulatorByOrganizationId(organizationId: Long): BigDecimal
}

@Repository
private interface TransactionRepo : JpaRepository<CustomerTransaction, Long> {

    fun findAllByOrganizationId(organizationId: Long): List<CustomerTransaction>

    fun findByIdAndOrganizationId(id: Long, organizationId: Long): CustomerTransaction?

    fun findAllByOrganizationIdAndCustomerId(
        organizationId: Long,
        customerId: Long
    ): List<CustomerTransaction>

    @Modifying
    @Query(
        value = "UPDATE customer_transactions SET deleted = NOW(), updated_by = :updatedBy WHERE id = :id AND organization_id = :organizationId AND deleted IS NULL",
        nativeQuery = true
    )
    fun deleteByIdAndOrganizationId(id: Long, organizationId: Long, updatedBy: Long)

    // SUM queries for aggregation
    @Query(
        value = "SELECT COALESCE(SUM(amount), 0) FROM customer_transactions WHERE organization_id = :organizationId AND deleted IS NULL",
        nativeQuery = true
    )
    fun sumAmountByOrganizationId(organizationId: Long): BigDecimal

    @Query(
        value = "SELECT COALESCE(SUM(amount_paid), 0) FROM customer_transactions WHERE organization_id = :organizationId AND deleted IS NULL",
        nativeQuery = true
    )
    fun sumAmountPaidByOrganizationId(organizationId: Long): BigDecimal

    @Query(
        value = "SELECT COALESCE(SUM(amount), 0) FROM customer_transactions WHERE organization_id = :organizationId AND customer_id = :customerId AND deleted IS NULL",
        nativeQuery = true
    )
    fun sumAmountByOrganizationIdAndCustomerId(organizationId: Long, customerId: Long): BigDecimal

    @Query(
        value = "SELECT COALESCE(SUM(amount_paid), 0) FROM customer_transactions WHERE organization_id = :organizationId AND customer_id = :customerId AND deleted IS NULL",
        nativeQuery = true
    )
    fun sumAmountPaidByOrganizationIdAndCustomerId(organizationId: Long, customerId: Long): BigDecimal
}
package com.project.management.beneficiaries

import com.project.management.common.configuration.CurrentUserConfig
import com.project.management.common.entity.OrganizationId
import com.project.management.common.entity.validate
import com.project.management.common.exceptions.BusinessException
import jakarta.transaction.Transactional
import org.springframework.stereotype.Service

@Service
class BeneficiaryService(
    private val currentUser: CurrentUserConfig,
) {

    fun getAll(): List<Beneficiary> {
        val user = currentUser.getCurrentUser()
        val beneficiaries = BeneficiaryRepository.Query.getAll(user)
        return beneficiaries
    }

    fun getById(beneficiaryId: Long): Beneficiary {
        val user = currentUser.getCurrentUser()
        val beneficiary = BeneficiaryRepository.Query.getById(user, beneficiaryId).validate()
        return beneficiary
    }

    @Transactional
    fun create(request: BeneficiaryPostRequest): Beneficiary {
        val user = currentUser.getCurrentUser()
        val beneficiary = request.toModel(user)

        return BeneficiaryRepository.Mutate.save(beneficiary)
    }

    @Transactional
    fun edit(
        request: BeneficiaryPatchPatch,
        beneficiaryId: Long
    ): Beneficiary {
        val loggedInUser = currentUser.getCurrentUser()
        val beneficiary = BeneficiaryRepository.Query
            .getById(loggedInUser, beneficiaryId).validate()

        if (beneficiary.version != request.version) throw BusinessException.ConflictException()

        if (request.name != null) beneficiary.name = request.name
        if (request.phoneNumber != null) {
            beneficiary.phoneNumber = request.phoneNumber
            beneficiary.secondaryPhoneNumber = request.phoneNumber
        }
        beneficiary.updatedBy = loggedInUser.id

        return BeneficiaryRepository.Mutate.save(beneficiary)
    }
}
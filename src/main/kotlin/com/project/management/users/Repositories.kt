package com.project.management.users

import com.project.management.common.entity.OrganizationId
import com.project.management.common.utility.autowired
import com.project.management.common.utility.filterDeleted
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.math.BigDecimal

object UserRepository {
    private val repo: SpringUserRepository by autowired()

    object Query {

        fun getByUsernameAndNotDeleted(organization: String, username: String): User? {
            return repo.findByUsernameAndOrganizationCodeAndNotDeleted(username, organization)
        }

        fun getByUsername(organizationCode: String, username: String): User? = filterDeleted {
            repo.findByUsernameAndOrganizationCode(username, organizationCode)
        }

        fun getAll(organizationId: OrganizationId): List<User> = filterDeleted {
            repo.findAllByOrganizationId(organizationId.value)
        }

        // TODO secure with organization id
        fun getAllByIds(ids: List<Long>): List<User> = filterDeleted {
            repo.findAllById(ids)
        }

        fun getById(organizationId: OrganizationId, id: Long): User? = filterDeleted {
            repo.findByIdAndOrganizationId(id, organizationId.value)
        }

        fun sumAllBalance(organizationId: OrganizationId): BigDecimal = filterDeleted {
            repo.sumBalanceByOrganizationId(organizationId.value)
        }
    }

    object Mutate {
        fun save(user: User): User {
            return repo.save(user)
        }

        fun deleteById(id: Long, organizationId: OrganizationId, updatedBy: Long) {
            repo.deleteByIdAndOrganizationId(id, organizationId.value, updatedBy)
        }
    }

    private fun <T> filterDeleted(block: () -> T) = filterDeleted(USER_DELETED_FILTER, block)
}

object BalanceTransactionRepository {
    private val repo: SpringBalanceTransactionRepository by autowired()

    object Query {
        fun getAll(organizationId: OrganizationId): List<BalanceTransaction> = filterDeleted {
            repo.findAllByOrganizationId(organizationId.value)
        }

        fun getAllByUserId(
            organizationId: OrganizationId, userId: Long
        ): List<BalanceTransaction> = filterDeleted {
            repo.findAllByUserIdAndOrganizationId(userId, organizationId.value)
        }
    }

    object Mutate {
        fun save(transaction: BalanceTransaction): BalanceTransaction {
            return repo.save(transaction)
        }
    }

    private fun <T> filterDeleted(block: () -> T): T {
        return filterDeleted(BALANCE_TRANSACTION_DELETED_FILTER, block)
    }
}


@Repository
private interface SpringUserRepository : JpaRepository<User, Long> {

    @Query(
        value = "SELECT * FROM users WHERE username = :username AND organization_code = :organizationCode AND deleted IS NULL",
        nativeQuery = true
    )
    fun findByUsernameAndOrganizationCodeAndNotDeleted(
        username: String,
        organizationCode: String,
    ): User?

    fun findByUsernameAndOrganizationCode(username: String, organizationCode: String): User?
    fun findAllByOrganizationId(organizationId: Long): List<User>
    fun findByIdAndOrganizationId(id: Long, organizationId: Long): User?

    @Modifying
    @Query(
        value = "UPDATE users SET deleted = NOW(), updated_by = :updatedBy WHERE id = :id AND organization_id = :organizationId AND deleted IS NULL",
        nativeQuery = true
    )
    fun deleteByIdAndOrganizationId(id: Long, organizationId: Long, updatedBy: Long)

    // SUM queries for aggregation
    @Query(
        value = "SELECT COALESCE(SUM(balance), 0) FROM users WHERE organization_id = :organizationId AND deleted IS NULL",
        nativeQuery = true
    )
    fun sumBalanceByOrganizationId(organizationId: Long): BigDecimal
}

@Repository
private interface SpringBalanceTransactionRepository : JpaRepository<BalanceTransaction, Long> {

    fun findAllByOrganizationId(organizationId: Long): List<BalanceTransaction>

    fun findAllByUserIdAndOrganizationId(
        userId: Long,
        organizationId: Long
    ): List<BalanceTransaction>

    // SUM queries for aggregation
    @Query(
        value = "SELECT COALESCE(SUM(amount), 0) FROM balance_transactions WHERE organization_id = :organizationId AND deleted IS NULL",
        nativeQuery = true
    )
    fun sumAmountByOrganizationId(organizationId: Long): BigDecimal

    @Query(
        value = "SELECT COALESCE(SUM(amount), 0) FROM balance_transactions WHERE organization_id = :organizationId AND user_id = :userId AND deleted IS NULL",
        nativeQuery = true
    )
    fun sumAmountByOrganizationIdAndUserId(organizationId: Long, userId: Long): BigDecimal

    @Query(
        value = "SELECT COALESCE(SUM(amount), 0) FROM balance_transactions WHERE organization_id = :organizationId AND transaction_tag = :tag AND deleted IS NULL",
        nativeQuery = true
    )
    fun sumAmountByOrganizationIdAndTag(organizationId: Long, tag: String): BigDecimal
}